package com.heal.controlcenter.dao.mysql;

import com.heal.configuration.pojos.BasicEntity;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.pojo.AgentInstanceMappingPojo;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.AgentSnapshotDetailsPojo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;

@Slf4j
@Repository
public class AgentDao {

    private final JdbcTemplate jdbcTemplate;

    public AgentDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public Set<AgentBean> getAgentTypeListApartFromJimAndForensicAgents(Set<Integer> agentIds) throws HealControlCenterException {
        String query = "SELECT id, unique_token uniqueToken, name, agent_type_id agentTypeId, created_time " +
                "createdTime, updated_time updatedTime, user_details_id lastModifiedBy, status, " +
                "host_address hostAddress, physical_agent_id physicalAgentId, mode, description FROM agent " +
                "WHERE agent_type_id != 2 and agent_type_id != 218 and id IN (?)";
        try {
            log.debug("getting agent data.");
            return new HashSet<>(jdbcTemplate.query(query, new BeanPropertyRowMapper<>(AgentBean.class), agentIds));
        } catch (Exception e) {
            log.error("Error occurred while fetching agent information for agentIds {}. Details: ", agentIds.toString(), e);
            throw new HealControlCenterException("Error occurred while fetching agent information.");
        }
    }

    public List<PhysicalAgentBean> getPhysicalAgentsForOngoingCommand() throws HealControlCenterException {
        List<PhysicalAgentBean> getPhysicalAgentsList = null;
        try {
            String GET_PHYSICAL_AGENTS_QUERY = "select id,last_job_id lastJobId,identifier identifier,user_details_id userDetailsId,is_command_executed lastCommandExecuted " + "\n" +
                    "from physical_agent where is_command_executed=0";
            getPhysicalAgentsList = jdbcTemplate.query(GET_PHYSICAL_AGENTS_QUERY, new BeanPropertyRowMapper<>(PhysicalAgentBean.class));
        } catch (Exception e) {
            String ERROR_GETTING_PHYSICAL_AGENTS = "Error while getting physical agents";
            log.error("{}.Reason: ", ERROR_GETTING_PHYSICAL_AGENTS, e);
            throw new HealControlCenterException(ERROR_GETTING_PHYSICAL_AGENTS);
        }
        return getPhysicalAgentsList;
    }

    public CommandTriggerBean getAgentCommandTriggerStatus(int physicalAgentId, String commandJobId) throws HealControlCenterException {
        List<CommandTriggerBean> commandTriggerBeanList = new ArrayList<>();
        try {
            String GET_AGENT_COMMAND_TRIGGER_STATUS_QUERY =
                    "SELECT cd.name lastCommandName, mst.name desiredStat, cd.timeout_in_secs timeoutInSecs, " +
                            "acm.trigger_time triggerTime, " +
                            "(SELECT COUNT(*) FROM agent_commands_triggered act WHERE act.physical_agent_id = ?) AS noOfCmds, " +
                            "acm.command_id commandId " +
                            "FROM agent_commands_triggered acm " +
                            "JOIN command_details cd ON acm.command_id = cd.id " +
                            "JOIN mst_sub_type mst ON cd.action_id = mst.id " +
                            "WHERE acm.command_job_id = ? AND acm.physical_agent_id = ?";

            commandTriggerBeanList = jdbcTemplate.query(
                    GET_AGENT_COMMAND_TRIGGER_STATUS_QUERY,
                    new BeanPropertyRowMapper<>(CommandTriggerBean.class),
                    physicalAgentId, commandJobId, physicalAgentId
            );
        } catch (Exception e) {
            String ERROR_GETTING_AGENT_COMMAND_TRIGGER_STATUS = "Error while getting agent command trigger status";
            log.error("{}. Reason: ", ERROR_GETTING_AGENT_COMMAND_TRIGGER_STATUS, e);
            throw new HealControlCenterException(ERROR_GETTING_AGENT_COMMAND_TRIGGER_STATUS);
        }
        if (commandTriggerBeanList.isEmpty()) {
            return null;
        } else {
            return commandTriggerBeanList.get(0);
        }
    }


    public String getAccountIdentifier(int physicalAgentId) throws HealControlCenterException {
        String accountIdentifier = null;
        try {
            String GET_ACCOUNT_IDENTIFIER_QUERY = "select identifier from account where id=(select distinct account_id from agent_account_mapping where " +
                    "agent_id in (select id from agent where physical_agent_id=" + physicalAgentId + "))";
            accountIdentifier = jdbcTemplate.queryForObject(GET_ACCOUNT_IDENTIFIER_QUERY, String.class);
        } catch (Exception e) {
            String ERROR_GETTING_ACCOUNT_IDENTIFIER = "Error while getting account identifier";
            log.error("{}.Reason: ", ERROR_GETTING_ACCOUNT_IDENTIFIER, e);
            throw new HealControlCenterException(ERROR_GETTING_ACCOUNT_IDENTIFIER);
        }
        return accountIdentifier;
    }

    public List<CommandArgumentBean> getServiceCommandArguments(int serviceId, int agentTypeId, int commandId) throws HealControlCenterException {
        String query = "SELECT ifnull(scm.id, cm.id) id, scm.argument_value value, ifnull(scm.argument_value, cm.default_value) " +
                "defaultValue, cm.argument_key argumentKey, cm.id commandId FROM service_command_arguments scm RIGHT JOIN " +
                "command_arguments cm ON cm.id = scm.command_argument_id AND scm.service_id = ? AND scm.agent_type_id = ? " +
                "WHERE cm.command_id = ?";
        try {
            log.debug("getting command arguments.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(CommandArgumentBean.class), serviceId, agentTypeId, commandId);
        } catch (Exception e) {
            log.error("Error occurred while fetching command arguments from 'service_command_arguments' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching command arguments.");
        }
    }

    public AgentModeConfigBean getAgentModeConfig(int serviceId, int accountId, int agentTypeId) throws HealControlCenterException {
        String query = "SELECT id, service_id serviceId, agent_type_id agentTypeId, command_id commandId, " +
                "created_time createdTime, updated_time updatedTime, user_details_id lastModifiedBy, account_id accountId " +
                "FROM agent_mode_configuration " +
                "WHERE service_id = ? and account_id = ? and agent_type_id = ?";
        try {
            log.debug("getting agent configurations.");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(AgentModeConfigBean.class), serviceId, accountId, agentTypeId);
        } catch (Exception e) {
            log.error("Error occurred while fetching agent configurations from 'agent_mode_configuration' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching agent configurations.");
        }
    }

    public List<AgentBean> getAgentList() throws HealControlCenterException {
        String query = "SELECT id, unique_token uniqueToken, physical_agent_id physicalAgentId, name, agent_type_id agentTypeId, created_time " +
                "createdTime, updated_time updatedTime, user_details_id lastModifiedBy, status, " +
                "host_address hostAddress, mode, description, supervisor_id supervisorId FROM agent";
        try {
            log.debug("getting tag mapping details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(AgentBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching agent details from 'agent' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching agent details.");
        }
    }

    public AgentSnapshotDetailsPojo getAgentSnapshotDetails(String autoSnapshotCollectionLevel) throws HealControlCenterException {
        String query = "SELECT command_mode agentMode, duration_in_mins snapshotDuration, no_of_snapshots snapshotcount, " +
                "silent_window_in_minis silentWindow FROM snapshot_levels WHERE identifier = ?";
        try {
            log.debug("getting agent snapshot details.");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(AgentSnapshotDetailsPojo.class), autoSnapshotCollectionLevel);
        } catch (Exception e) {
            log.error("Error occurred while fetching agent snapshot details from 'snapshot_levels' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching agent snapshot details.");
        }
    }

    public void updateAgentModeConfig(AgentModeConfigBean configBean) throws HealControlCenterException {
        String query = "UPDATE agent_mode_configuration SET command_id = ?, updated_time = ?, " +
                "user_details_id = ? WHERE service_id = ? AND account_id = ? AND " +
                "agent_type_id = ?";
        try {
            log.debug("updating agent mode details.");
            jdbcTemplate.update(query, configBean.getCommandId(), configBean.getUpdatedTime(),
                    configBean.getLastModifiedBy(), configBean.getServiceId(), configBean.getAccountId(), configBean.getAgentTypeId());
        } catch (Exception e) {
            log.error("Exception encountered while updating agent mode details in 'agent_mode_configuration' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while updating agent mode details.");
        }
    }

    public void addAgentModeConfig(AgentModeConfigBean configBean) throws HealControlCenterException {
        String query = "INSERT INTO agent_mode_configuration (service_id, agent_type_id, command_id, " +
                "created_time, updated_time, user_details_id, " +
                "account_id) VALUES (?, ?, ?, ?, ?, ?, ?)";
        try {
            log.debug("inserting agent mode details.");
            jdbcTemplate.update(query, configBean.getServiceId(), configBean.getAgentTypeId(),
                    configBean.getCommandId(), configBean.getCreatedTime(), configBean.getUpdatedTime(), configBean.getLastModifiedBy(), configBean.getAccountId());
        } catch (Exception e) {
            log.error("Exception encountered while inserting agent mode details in 'agent_mode_configuration' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while inserting agent mode details.");
        }
    }

    public void updateServiceCommandArguments(List<ServiceCommandArgumentBean> updateCmdArgs) throws HealControlCenterException {
        String query = "UPDATE service_command_arguments SET argument_value = ?, updated_time = ?, " +
                "user_details_id = ? WHERE id = ? AND service_id = ? AND agent_type_id = ?";
        try {
            log.debug("updating service command arguments.");
            jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ServiceCommandArgumentBean cmdArgs = updateCmdArgs.get(i);
                    ps.setString(1, cmdArgs.getArgumentValue());
                    ps.setString(2, cmdArgs.getUpdatedTime());
                    ps.setString(3, cmdArgs.getLastModifiedBy());
                    ps.setInt(4, cmdArgs.getId());
                    ps.setInt(5, cmdArgs.getServiceId());
                    ps.setInt(6, cmdArgs.getAgentTypeId());
                }

                @Override
                public int getBatchSize() {
                    return updateCmdArgs.size();
                }
            });
        } catch (Exception e) {
            log.error("Exception encountered while updating service command arguments in 'service_command_arguments' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while updating service command arguments.");
        }
    }

    public void addServiceCommandArguments(List<ServiceCommandArgumentBean> insertCmdArgs) throws HealControlCenterException {
        String query = "INSERT INTO service_command_arguments (agent_type_id, service_id, command_id, command_argument_id, " +
                "argument_value, created_time, updated_time, user_details_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        try {
            log.debug("inserting service command arguments.");
            jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ServiceCommandArgumentBean cmdArgs = insertCmdArgs.get(i);
                    ps.setInt(1, cmdArgs.getAgentTypeId());
                    ps.setInt(2, cmdArgs.getServiceId());
                    ps.setInt(3, cmdArgs.getCommandId());
                    ps.setInt(4, cmdArgs.getCommandArgumentId());
                    ps.setString(5, cmdArgs.getArgumentValue());
                    ps.setString(6, cmdArgs.getCreatedTime());
                    ps.setString(7, cmdArgs.getUpdatedTime());
                    ps.setString(8, cmdArgs.getLastModifiedBy());
                }

                @Override
                public int getBatchSize() {
                    return insertCmdArgs.size();
                }
            });
        } catch (Exception e) {
            log.error("Exception encountered while inserting service command arguments in 'service_command_arguments' table. Details: ", e);
            throw new HealControlCenterException("Error occurred while inserting service command arguments.");
        }
    }

    public List<AgentInstanceMappingPojo> getInstanceAgentMapping(int accountId) {
        String query = "SELECT acim.comp_instance_id AS instanceId, acim.agent_id AS agentId, " +
                "ag.name AS agentName, ag.unique_token AS uniqueToken, " +
                "pa.id AS physicalAgentId, pa.identifier AS physicalAgentIdentifier, " +
                "mst.id AS agentTypeId, mst.name AS agentTypeName, mst.status AS status " +
                "FROM agent_comp_instance_mapping acim, mst_sub_type mst, agent ag, physical_agent pa, agent_account_mapping aam " +
                "WHERE acim.agent_id = ag.id AND aam.agent_id = ag.id AND aam.account_id = ? " +
                "AND ag.agent_type_id = mst.id AND ag.physical_agent_id = pa.id " +
                "AND mst.mst_type_id = 1 AND mst.status = 1";

        try {
            log.debug("Fetching instance-agent mapping for accountId: {}", accountId);
            return jdbcTemplate.query(query,
                    new BeanPropertyRowMapper<>(AgentInstanceMappingPojo.class),
                    accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching instance-agent mappings for accountId: {}. Details:", accountId, e);
            return Collections.emptyList();
        }
    }

    /**
     * Gets agent bean data by identifier.
     * Original JDBI query: "select a.id,unique_token uniqueToken,a.name,a.agent_type_id agentTypeId,a.created_time createdTime,a.updated_time updatedTime,a.user_details_id userDetailsId,a.status, a.host_address hostAddress, a.mode, a.description, a.physical_agent_id physicalAgentId, a.forensics_enabled forensicsEnabled, a.version, pa.identifier physicalAgentIdentifier from agent a, physical_agent pa where unique_token = :agentUid and a.physical_agent_id=pa.id"
     * @param agentUid The agent unique token
     * @return AgentBean if found, null otherwise
     */
    public AgentBean getAgentBeanData(String agentUid) {
        String sql = "SELECT a.id, unique_token as uniqueToken, a.name, a.agent_type_id as agentTypeId, " +
                "a.created_time as createdTime, a.updated_time as updatedTime, a.user_details_id as userDetailsId, " +
                "a.status, a.host_address as hostAddress, a.mode, a.description, a.physical_agent_id as physicalAgentId, " +
                "a.forensics_enabled as forensicsEnabled, a.version, pa.identifier as physicalAgentIdentifier " +
                "FROM agent a " +
                "JOIN physical_agent pa ON a.physical_agent_id = pa.id " +
                "WHERE a.unique_token = ?";

        try {
            List<AgentBean> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                AgentBean bean = new AgentBean();
                bean.setId(rs.getInt("id"));
                bean.setUniqueToken(rs.getString("uniqueToken"));
                bean.setName(rs.getString("name"));
                bean.setAgentTypeId(rs.getInt("agentTypeId"));
                bean.setCreatedTime(rs.getString("createdTime"));
                bean.setUpdatedTime(rs.getString("updatedTime"));
                bean.setUserDetailsId(rs.getString("userDetailsId"));
                bean.setLastModifiedBy(rs.getString("userDetailsId")); // Map to lastModifiedBy as well
                bean.setStatus(rs.getInt("status"));
                bean.setHostAddress(rs.getString("hostAddress"));
                bean.setMode(rs.getString("mode"));
                bean.setDescription(rs.getString("description"));
                bean.setPhysicalAgentId(rs.getInt("physicalAgentId"));
                bean.setForensicsEnabled(rs.getBoolean("forensicsEnabled"));
                bean.setVersion(rs.getString("version"));
                bean.setPhysicalAgentIdentifier(rs.getString("physicalAgentIdentifier"));
                return bean;
            }, agentUid);

            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error getting agent bean data: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Gets agent to instance mapping using JDBC with proper joins.
     * @param instanceId The component instance ID
     * @param accountId The account ID
     * @param agentId The agent ID
     * @return List of BasicEntity objects representing the mapping
     */
    public List<BasicEntity> getAgentToInstanceMapping(int instanceId, int accountId, int agentId) {
        String sql = "SELECT c.id, c.name, c.identifier, " +
                "c.created_time AS createdTime, c.updated_time AS updatedTime, " +
                "c.user_details_id AS lastModifiedBy, a.status " +
                "FROM agent_comp_instance_mapping acim " +
                "JOIN comp_instance c ON c.id = acim.comp_instance_id " +
                "JOIN agent a ON a.id = acim.agent_id " +
                "WHERE acim.agent_id = ? AND c.account_id = ? AND c.id = ?";

        try {
            return jdbcTemplate.query(sql, (rs, rowNum) -> {
                BasicEntity entity = new BasicEntity();
                entity.setId(rs.getInt("id"));
                entity.setName(rs.getString("name"));
                entity.setIdentifier(rs.getString("identifier"));
                entity.setCreatedTime(rs.getString("createdTime"));
                entity.setUpdatedTime(rs.getString("updatedTime"));
                entity.setLastModifiedBy(rs.getString("lastModifiedBy"));
                entity.setStatus(rs.getInt("status"));
                return entity;
            }, agentId, accountId, instanceId);
        } catch (Exception e) {
            log.error("Error occurred while fetching agent to instance mapping for agentId: {}, accountId: {}, instanceId: {}. Details: ",
                    agentId, accountId, instanceId, e);
            return Collections.emptyList();
        }
    }

}
