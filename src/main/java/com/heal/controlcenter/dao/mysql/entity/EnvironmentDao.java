package com.heal.controlcenter.dao.mysql.entity;

import com.heal.controlcenter.beans.ObjPojo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * DAO for environment operations.
 */
@Slf4j
@Repository
public class EnvironmentDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Gets environment type ID from type name.
     * @param typeName The type name
     * @return Type ID or 0 if not found
     */
    public int getEnvTypeIdFromTypeName(String typeName) {
        String sql = "SELECT id FROM mst_type WHERE type = ?";

        try {
            Integer typeId = jdbcTemplate.queryForObject(sql, Integer.class, typeName);
            return typeId != null ? typeId : 0;
        } catch (Exception e) {
            log.error("Error getting environment type ID: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * Gets environment subtype details.
     * @param environmentTypeId The environment type ID
     * @return List of ObjPojo with subtype details
     */
    public List<ObjPojo> getEnvSubTypeDetails(int environmentTypeId) {
        String sql = "SELECT sub_type_id as id, sub_type_name as name FROM mst_sub_type WHERE mst_type_id = ?";

        @SqlQuery("select id, name from mst_sub_type where account_id = 1 and status = 1 and mst_type_id = :typeId")
        List<ObjPojo> getEnvSubTypeDetails(@Bind("typeId") int typeId);

        try {
            return jdbcTemplate.query(sql, (rs, rowNum) -> {
                ObjPojo pojo = new ObjPojo();
                pojo.setId(rs.getInt("id"));
                pojo.setName(rs.getString("name"));
                return pojo;
            }, environmentTypeId);
        } catch (Exception e) {
            log.error("Error getting environment sub type details: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
