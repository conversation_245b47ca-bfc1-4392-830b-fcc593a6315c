package com.heal.controlcenter.dao.mysql.entity;

import com.heal.controlcenter.beans.CompClusterMappingBean;
import com.heal.controlcenter.beans.CompInstanceKpiGroupDetailsBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class ComponentDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public List<CompClusterMappingBean> getInstanceClusterMapping(int accountId) {
        String query = "SELECT comp_instance_id AS compInstanceId, cluster_id AS clusterId " +
                "FROM component_cluster_mapping " +
                "WHERE account_id = ?";

        try {
            log.debug("Fetching instance-cluster mappings for accountId: {}", accountId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(CompClusterMappingBean.class), accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching instance-cluster mappings for accountId: {}. Details:", accountId, e);
            return Collections.emptyList();
        }
    }

    /**
     * Gets config watch files/KPIs by component ID and version ID.
     * This method retrieves KPI group details for file/config watch monitoring.
     *
     * @param componentId The master component ID
     * @param componentVersionId The master component version ID
     * @return List of CompInstanceKpiGroupDetailsBean for config watch KPIs
     */
    public List<CompInstanceKpiGroupDetailsBean> getConfigWatchFilesByComponentId(int componentId, int componentVersionId) {
        String query = "SELECT " +
                "kgd.mst_kpi_details_id as mstKpiDetailsId, " +
                "kgd.mst_kpi_group_id as mstKpiGroupId, " +
                "kgd.mst_producer_kpi_mapping_id as mstProducerKpiMappingId, " +
                "kgd.collection_interval as collectionInterval, " +
                "kgd.kpi_group_name as kpiGroupName, " +
                "kgd.mst_producer_id as mstProducerId, " +
                "kgd.attribute_value as attributeValue, " +
                "kgd.is_discovery as isDiscovery " +
                "FROM mst_kpi_group_details kgd " +
                "JOIN mst_component_version_kpi_mapping cvkm ON kgd.mst_kpi_details_id = cvkm.mst_kpi_details_id " +
                "JOIN mst_kpi_details kd ON kgd.mst_kpi_details_id = kd.id " +
                "WHERE cvkm.mst_component_id = ? " +
                "AND cvkm.mst_component_version_id = ? " +
                "AND kd.kpi_type_id IN (SELECT id FROM mst_sub_type WHERE name IN ('Config Watch', 'File Watch')) " +
                "AND cvkm.status = 1 " +
                "AND kgd.status = 1";

        try {
            log.debug("Fetching config watch files for componentId: {} and componentVersionId: {}", componentId, componentVersionId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(CompInstanceKpiGroupDetailsBean.class), componentId, componentVersionId);
        } catch (Exception e) {
            log.error("Error occurred while fetching config watch files for componentId: {} and componentVersionId: {}. Details:",
                    componentId, componentVersionId, e);
            return Collections.emptyList();
        }
    }
}
