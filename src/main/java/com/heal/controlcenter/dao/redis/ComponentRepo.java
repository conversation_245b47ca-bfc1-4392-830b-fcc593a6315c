package com.heal.controlcenter.dao.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Component;
import com.heal.configuration.pojos.ComponentKpiEntity;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class ComponentRepo {

    @Autowired
    private RedisUtilities redisUtilities;

    String ACCOUNTS_KEY = "/accounts";
    String COMPONENT_KEY = "/components";
    String ACCOUNTS_HASH = "ACCOUNTS";
    String COMPONENT_HASH = "_COMPONENTS";
    String KPI_KEY = "/kpis";
    String KPIS_HASH = "_KPIS";
    public List<Component> getComponentDetails(String accountIdentifier)
    {
        try {
            String componentObject = redisUtilities.getKey(ACCOUNTS_KEY + "/" + accountIdentifier + COMPONENT_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + COMPONENT_HASH );
            if (componentObject == null) {
                log.debug("Component  details not found for account: [{}]", accountIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(componentObject, new TypeReference<List<Component>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting  Component details for account [{}]. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<ComponentKpiEntity> getComponentKpiDetails(String accountIdentifier, String componentName)
    {
        try {
            String componentKpiObject = redisUtilities.getKey(ACCOUNTS_KEY + "/" + accountIdentifier + COMPONENT_KEY + "/" + componentName + KPI_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + COMPONENT_HASH + "_" + componentName + KPIS_HASH);
            if (componentKpiObject == null) {
                log.debug("Component Kpi details not found for account: [{}]", accountIdentifier);
                return new ArrayList<>();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(componentKpiObject, new TypeReference<List<ComponentKpiEntity>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting  Component kpi details for account [{}]. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public void updateComponentKpiDetails(List<ComponentKpiEntity> componentKpiEntityList, String accountIdentifier, String componentName) {
        try {
            redisUtilities.updateKey(ACCOUNTS_KEY + "/" +  accountIdentifier + COMPONENT_KEY + "/" + componentName + KPI_KEY, ACCOUNTS_HASH  + "_" + accountIdentifier + COMPONENT_HASH + "_" + componentName + KPIS_HASH, componentKpiEntityList);
        }catch (Exception e){
            log.error("Error occurred while updating the kpi details for the given accountIdentifier: {} and componentNamw: {}", accountIdentifier, componentName);
        }
    }
}
